LuaT �

xV           (w@�@.\ForcePlayerPortraits.lua�� �Q      R   �  R   �  � � � � �    �  R   � � � � � �   �    �  R    � �D� ���� �� �D�  R   ��������N �  
�  R   ����O�  � O � O� � O  O�  O  F���EN�Force Player Portraits�Force portraits or force using rendered pics, force portraits only for players who have portrait file�Failed to init Force Player Portraits�Rendered Pics�Portrait Files�ZH�强制球员照片�可以选择强制照片文件 或 强制使用渲染图片(泥人), 强制照片文件只对有照片文件的球员生效�强制球员照片初始化失败�渲染图片�照片文件�get�PluginInfo�name�author�Looyh�version�V1.0�description�requiredHookVersion�globalPortrait�85 DB 75 2E 48 85 C0 74�inMatches�75 ? 8B 4D 40 48 8D 45 D4 48 89 45 F0 48�writeAsm�setUsingRenderedPics�reset�Init�DeInit�OnDrawMenuItem�  ���� ��  � � 	  � �  �  �  ��Hook�getGuiLang�     ��      ���key����lang�_ENV���  �   B   8 �  R   ��   N  B�  8 �  R   ��  N  �  ��� � �  ��� � �  ��usingRenderedPics�FF C3 EB 2E�inMatches�31 DB 90 90�90 90�pAsmGlobalPortrait�writeByteArrayString�pAsmInMatches� ��                    ���forcePortraits����feature��� �   � � �   �  � �  � �  ��usingRenderedPics�Plugin�config�set�writeAsm�    ��      ���val����feature�_ENV���  �   �  D   �  D G  ��pAsmGlobalPortrait�writeByteArrayString�globalPortrait�pAsmInMatches�inMatches� ��      ����feature���  �   B�  8 � � � D��� �   D      � � D��� �   D      � 	� �D        
�  D  
   D G  ��pAsmGlobalPortrait�Pointer�assert�Hook�markCodeScan�globalPortrait�get�pAsmInMatches�inMatches�readByteArrayString�usingRenderedPics�Plugin�config�getBool�writeAsm�      ��                                  ����feature�_ENV�lang���  �   D G  ��reset� �� ����feature���  �     � �� 3 D B   8 � �   D      D      � ��� D B   8 � �   D G  ��ImGui�RadioButton�get�usingRenderedPics�setUsingRenderedPics�SameLine�      ��                       ����_ENV�lang�feature�        �              	��
�������lang���feature����_ENV