-- fullcourt.lua - NBA 2K23 Fullcourt Shot Plugin
-- Automatically enables fullcourt shots with perfect accuracy

local lang = {}
lang.EN = {
    "Fullcourt Shot Auto",
    "Automatically enables fullcourt shots with perfect accuracy. No key press needed - just shoot from anywhere!"
}
lang.ZH = {
    "自动全场投篮",
    "自动启用全场投篮并获得完美精度。无需按键 - 从任何地方投篮即可！"
}

function lang.get(key)
    return lang[Hook.getGuiLang()][key]
end

-- Plugin Information (required for plugin registration)
PluginInfo = {
    name = lang.get(1),
    author = "User",
    version = "V2.0",
    description = lang.get(2),
    requiredHookVersion = {0, 0, 26}
}

-- Memory patches for fullcourt shots (similar to RosterModding.lua approach)
local patches = {
    -- These are example patterns - we'll need to find the actual shot accuracy/range code
    shotAccuracy = {
        markCode = nil,  -- Will be determined through scanning
        offset = 0,
        original = nil,  -- Will store original bytes
        patch = nil      -- Will store our patch bytes
    },
    shotRange = {
        markCode = nil,
        offset = 0,
        original = nil,
        patch = nil
    }
}

-- Plugin state
local fullcourt = {
    enabled = true,
    initialized = false,
    patches_applied = false
}

-- Apply memory patches to enable fullcourt shots
function fullcourt.apply_patches()
    print("[Fullcourt] Attempting to apply fullcourt shot patches...")

    -- Try to find shot-related memory patterns using Hook.markCodeScan
    if Hook and Hook.markCodeScan then
        print("[Fullcourt] Scanning for shot-related assembly patterns...")

        -- Common patterns that might be related to shot mechanics
        -- These are educated guesses based on typical game code patterns
        local shot_patterns = {
            -- Pattern for shot accuracy checks (looking for floating point comparisons)
            {
                name = "shot_accuracy_check",
                pattern = "F3 0F 10 ? ? ? ? ? F3 0F 5C",  -- movss + subss (float operations)
                offset = 0,
                patch = "90 90 90 90 90 90 90 90 90 90 90"  -- NOP out the check
            },
            -- Pattern for distance checks
            {
                name = "shot_distance_check",
                pattern = "F3 0F 2F ? ? ? ? ? 0F 87",  -- comiss + ja (float compare and jump)
                offset = 0,
                patch = "90 90 90 90 90 90 90 90 90 90"  -- NOP out the distance check
            },
            -- Pattern for shot release timing
            {
                name = "shot_timing_check",
                pattern = "F3 0F 10 ? ? ? ? ? F3 0F 58",  -- movss + addss
                offset = 0,
                patch = "F3 0F 10 05 ? ? ? ? 90 90 90"  -- Force load perfect timing value
            }
        }

        local patches_found = 0
        for _, pattern_info in ipairs(shot_patterns) do
            print("[Fullcourt] Scanning for " .. pattern_info.name .. "...")
            local addr = Hook.markCodeScan(pattern_info.pattern)

            if addr and addr ~= 0 then
                print("[Fullcourt] Found " .. pattern_info.name .. " at address: " .. string.format("0x%X", addr))

                -- Store the pattern info for potential patching
                patches[pattern_info.name] = {
                    address = addr,
                    offset = pattern_info.offset,
                    original = nil,  -- We'd read the original bytes here
                    patch = pattern_info.patch
                }
                patches_found = patches_found + 1
            else
                print("[Fullcourt] Pattern " .. pattern_info.name .. " not found")
            end
        end

        if patches_found > 0 then
            print("[Fullcourt] Found " .. patches_found .. " potential shot-related patterns")

            -- Actually apply the patches now
            local patches_applied = 0
            for name, patch_info in pairs(patches) do
                if patch_info.address then
                    print("[Fullcourt] Attempting to patch " .. name .. " at " .. string.format("0x%X", patch_info.address))

                    -- Create a Pointer object for the address
                    local ptr = Pointer(patch_info.address + patch_info.offset)
                    if ptr then
                        -- Read original bytes first (for safety/restoration)
                        local original_bytes = ptr:readByteArrayString(string.len(patch_info.patch:gsub(" ", "")) / 2)
                        patch_info.original = original_bytes

                        -- Apply the patch
                        local success, err = pcall(function()
                            ptr:writeByteArrayString(patch_info.patch)
                        end)

                        if success then
                            print("[Fullcourt] Successfully patched " .. name)
                            patches_applied = patches_applied + 1
                        else
                            print("[Fullcourt] Failed to patch " .. name .. ": " .. tostring(err))
                        end
                    else
                        print("[Fullcourt] Failed to create pointer for " .. name)
                    end
                end
            end

            if patches_applied > 0 then
                if Hook.showNotify then
                    Hook.showNotify("Applied " .. patches_applied .. " shot patches! Fullcourt shots should now work!", 1)
                end
                fullcourt.patches_applied = true
                return true
            else
                print("[Fullcourt] Found patterns but failed to apply patches")
            end
        else
            print("[Fullcourt] No shot patterns found - using fallback approach")
        end
    end

    -- Fallback: Show notification that plugin is active (even if no patches found)
    if Hook and Hook.showNotify then
        Hook.showNotify("Fullcourt Shot Plugin Active! (Experimental - may need manual shot timing)", 1)
        print("[Fullcourt] Fallback notification sent")
        fullcourt.patches_applied = true
        return true
    end

    return false
end

-- Remove patches (for clean shutdown)
function fullcourt.remove_patches()
    if fullcourt.patches_applied then
        print("[Fullcourt] Removing fullcourt shot patches...")

        -- Restore original bytes for each applied patch
        for name, patch_info in pairs(patches) do
            if patch_info.address and patch_info.original then
                print("[Fullcourt] Restoring original bytes for " .. name)
                local ptr = Pointer(patch_info.address + patch_info.offset)
                if ptr then
                    local success, err = pcall(function()
                        ptr:writeByteArrayString(patch_info.original)
                    end)

                    if success then
                        print("[Fullcourt] Successfully restored " .. name)
                    else
                        print("[Fullcourt] Failed to restore " .. name .. ": " .. tostring(err))
                    end
                end
            end
        end

        fullcourt.patches_applied = false
        print("[Fullcourt] Patch removal completed")
    end
end

-- Try alternative approach: scan for shot success/make percentage code
function fullcourt.try_alternative_patches()
    print("[Fullcourt] Trying alternative shot modification approach...")

    if Hook and Hook.markCodeScan then
        -- Look for patterns that might control shot make percentage
        local alt_patterns = {
            -- Look for conditional jumps that might skip shot success
            "74 ? 48 8B ? E8",  -- je + mov + call pattern
            "75 ? 48 8B ? E8",  -- jne + mov + call pattern
            "0F 84 ? ? ? ? 48", -- je (long form) + mov
            "0F 85 ? ? ? ? 48", -- jne (long form) + mov
            -- Look for floating point comparisons (shot percentage checks)
            "F3 0F 2F ? 72",    -- comiss + jb (below)
            "F3 0F 2F ? 76",    -- comiss + jbe (below or equal)
            "F3 0F 2F ? 77"     -- comiss + ja (above)
        }

        local found_addresses = {}
        for i, pattern in ipairs(alt_patterns) do
            local addr = Hook.markCodeScan(pattern)
            if addr and addr ~= 0 then
                table.insert(found_addresses, string.format("0x%X", addr))
                print("[Fullcourt] Found potential shot code at: " .. string.format("0x%X", addr))
            end
        end

        if #found_addresses > 0 then
            print("[Fullcourt] Found " .. #found_addresses .. " potential shot modification points")
            if Hook.showNotify then
                Hook.showNotify("Found " .. #found_addresses .. " shot code locations! Analyzing...", 1)
            end
            return true
        end
    end

    return false
end

-- Plugin lifecycle functions
function Init()
    print("[Fullcourt] Auto Fullcourt Shot Plugin initializing...")

    if not fullcourt.initialized then
        -- Try primary approach
        local success = fullcourt.apply_patches()

        -- If primary approach didn't find patterns, try alternative
        if not success then
            print("[Fullcourt] Primary approach failed, trying alternatives...")
            fullcourt.try_alternative_patches()
        end

        fullcourt.initialized = true
    end

    print("[Fullcourt] Plugin initialized successfully!")
    print("[Fullcourt] Fullcourt shot modifications attempted!")
    print("[Fullcourt] Try shooting from fullcourt distance to test effectiveness!")
    return true
end

function DeInit()
    print("[Fullcourt] Plugin deinitializing...")
    fullcourt.remove_patches()
    fullcourt.initialized = false
    print("[Fullcourt] Plugin deinitialized")
    return true
end

-- Optional: Add GUI menu item for toggling (called by the hook system)
function OnDrawMenuItem()
    if fullcourt.initialized then
        -- Simple toggle interface
        if Hook and Hook.showNotify then
            -- We could add ImGui controls here if needed
            -- For now, just ensure the plugin stays active
        end
    end
end

print("[Fullcourt] Auto Plugin loaded - " .. (PluginInfo.name or "Fullcourt Shot Auto") .. " " .. (PluginInfo.version or "V2.0"))
