-- fullcourt.lua - NBA 2K23 Fullcourt Shot Plugin
-- Automatically enables fullcourt shots with perfect accuracy

local lang = {}
lang.EN = {
    "Fullcourt Shot Auto",
    "Automatically enables fullcourt shots with perfect accuracy. No key press needed - just shoot from anywhere!"
}
lang.ZH = {
    "自动全场投篮",
    "自动启用全场投篮并获得完美精度。无需按键 - 从任何地方投篮即可！"
}

function lang.get(key)
    return lang[Hook.getGuiLang()][key]
end

-- Plugin Information (required for plugin registration)
PluginInfo = {
    name = lang.get(1),
    author = "User",
    version = "V2.0",
    description = lang.get(2),
    requiredHookVersion = {0, 0, 26}
}

-- Memory patches for fullcourt shots (similar to RosterModding.lua approach)
local patches = {
    -- These are example patterns - we'll need to find the actual shot accuracy/range code
    shotAccuracy = {
        markCode = nil,  -- Will be determined through scanning
        offset = 0,
        original = nil,  -- Will store original bytes
        patch = nil      -- Will store our patch bytes
    },
    shotRange = {
        markCode = nil,
        offset = 0,
        original = nil,
        patch = nil
    }
}

-- Plugin state
local fullcourt = {
    enabled = true,
    initialized = false,
    patches_applied = false
}

-- Apply memory patches to enable fullcourt shots
function fullcourt.apply_patches()
    print("[Fullcourt] Attempting to apply fullcourt shot patches...")

    -- Try to find shot-related memory patterns using Hook.markCodeScan
    if Hook and Hook.markCodeScan then
        print("[Fullcourt] Scanning for shot-related assembly patterns...")

        -- Try more specific patterns based on NBA 2K shooting mechanics
        -- Focus on patterns that are more likely to be actual shot success checks
        local shot_patterns = {
            -- Look for shot success probability calculations
            {
                name = "shot_success_calc",
                pattern = "F3 0F 59 ? ? ? ? ? F3 0F 2F",  -- mulss + comiss (multiply and compare)
                offset = 0,
                patch = "B8 01 00 00 00 90 90 90 90 90 90"  -- mov eax, 1 + NOPs (force success)
            },
            -- Look for shot make/miss determination
            {
                name = "shot_make_check",
                pattern = "0F 2F ? ? ? ? ? 72",  -- comiss + jb (compare and jump if below)
                offset = 7,
                patch = "EB"  -- Change conditional jump to unconditional (always make)
            },
            -- Look for distance-based shot percentage reduction
            {
                name = "distance_penalty",
                pattern = "F3 0F 59 ? ? ? ? ? F3 0F 11",  -- mulss + movss (multiply distance factor)
                offset = 0,
                patch = "F3 0F 10 05 ? ? ? ? 90 90 90"  -- Load 1.0 instead of distance factor
            },
            -- Look for green window/perfect release checks
            {
                name = "perfect_release",
                pattern = "74 ? F3 0F 10 ? ? ? ? ?",  -- je + movss (jump if perfect)
                offset = 0,
                patch = "90 90"  -- NOP the conditional jump (always perfect)
            }
        }

        local patches_found = 0
        for _, pattern_info in ipairs(shot_patterns) do
            print("[Fullcourt] Scanning for " .. pattern_info.name .. "...")
            local addr = Hook.markCodeScan(pattern_info.pattern)

            if addr and addr ~= 0 then
                print("[Fullcourt] Found " .. pattern_info.name .. " at address: " .. string.format("0x%X", addr))

                -- Store the pattern info for potential patching
                patches[pattern_info.name] = {
                    address = addr,
                    offset = pattern_info.offset,
                    original = nil,  -- We'd read the original bytes here
                    patch = pattern_info.patch
                }
                patches_found = patches_found + 1
            else
                print("[Fullcourt] Pattern " .. pattern_info.name .. " not found")
            end
        end

        if patches_found > 0 then
            print("[Fullcourt] Found " .. patches_found .. " potential shot-related patterns")

            -- Actually apply the patches now
            local patches_applied = 0
            for name, patch_info in pairs(patches) do
                if patch_info.address then
                    print("[Fullcourt] Attempting to patch " .. name .. " at " .. string.format("0x%X", patch_info.address))

                    -- Create a Pointer object for the address
                    local ptr = Pointer(patch_info.address + patch_info.offset)
                    if ptr then
                        -- Read original bytes first (for safety/restoration)
                        local original_bytes = ptr:readByteArrayString(string.len(patch_info.patch:gsub(" ", "")) / 2)
                        patch_info.original = original_bytes

                        -- Apply the patch
                        local success, err = pcall(function()
                            ptr:writeByteArrayString(patch_info.patch)
                        end)

                        if success then
                            print("[Fullcourt] Successfully patched " .. name)
                            patches_applied = patches_applied + 1
                        else
                            print("[Fullcourt] Failed to patch " .. name .. ": " .. tostring(err))
                        end
                    else
                        print("[Fullcourt] Failed to create pointer for " .. name)
                    end
                end
            end

            if patches_applied > 0 then
                if Hook.showNotify then
                    Hook.showNotify("Applied " .. patches_applied .. " shot patches! Fullcourt shots should now work!", 1)
                end
                fullcourt.patches_applied = true
                return true
            else
                print("[Fullcourt] Found patterns but failed to apply patches")
            end
        else
            print("[Fullcourt] No shot patterns found - using fallback approach")
        end
    end

    -- Fallback: Show notification that plugin is active (even if no patches found)
    if Hook and Hook.showNotify then
        Hook.showNotify("Fullcourt Shot Plugin Active! (Experimental - may need manual shot timing)", 1)
        print("[Fullcourt] Fallback notification sent")
        fullcourt.patches_applied = true
        return true
    end

    return false
end

-- Remove patches (for clean shutdown)
function fullcourt.remove_patches()
    if fullcourt.patches_applied then
        print("[Fullcourt] Removing fullcourt shot patches...")

        -- Restore original bytes for each applied patch
        for name, patch_info in pairs(patches) do
            if patch_info.address and patch_info.original then
                print("[Fullcourt] Restoring original bytes for " .. name)
                local ptr = Pointer(patch_info.address + patch_info.offset)
                if ptr then
                    local success, err = pcall(function()
                        ptr:writeByteArrayString(patch_info.original)
                    end)

                    if success then
                        print("[Fullcourt] Successfully restored " .. name)
                    else
                        print("[Fullcourt] Failed to restore " .. name .. ": " .. tostring(err))
                    end
                end
            end
        end

        fullcourt.patches_applied = false
        print("[Fullcourt] Patch removal completed")
    end
end

-- Try a completely different approach: look for shot percentage/success rate values
function fullcourt.try_percentage_patches()
    print("[Fullcourt] Trying to find and modify shot percentage values...")

    if Hook and Hook.markCodeScan then
        -- Look for common floating point values used in shot calculations
        local percentage_patterns = {
            -- Look for 0.5 (50% success rate) - common baseline
            {pattern = "00 00 00 3F", name = "50_percent", new_value = "00 00 80 3F"},  -- Change 0.5 to 1.0
            -- Look for distance multipliers (values like 0.8, 0.9)
            {pattern = "CD CC CC 3E", name = "40_percent", new_value = "00 00 80 3F"},  -- Change 0.4 to 1.0
            {pattern = "66 66 66 3F", name = "90_percent", new_value = "00 00 80 3F"},  -- Change 0.9 to 1.0
        }

        local modified_count = 0
        for _, pct_info in ipairs(percentage_patterns) do
            local addr = Hook.markCodeScan(pct_info.pattern)
            if addr and addr ~= 0 then
                print("[Fullcourt] Found " .. pct_info.name .. " value at: " .. string.format("0x%X", addr))

                local ptr = Pointer(addr)
                if ptr then
                    local success, err = pcall(function()
                        ptr:writeByteArrayString(pct_info.new_value)
                    end)

                    if success then
                        print("[Fullcourt] Modified " .. pct_info.name .. " to 100%")
                        modified_count = modified_count + 1
                    else
                        print("[Fullcourt] Failed to modify " .. pct_info.name .. ": " .. tostring(err))
                    end
                end
            end
        end

        if modified_count > 0 then
            if Hook.showNotify then
                Hook.showNotify("Modified " .. modified_count .. " shot percentage values to 100%!", 1)
            end
            return true
        end
    end

    return false
end

-- Try to find and patch shot animation/result determination
function fullcourt.try_result_patches()
    print("[Fullcourt] Trying to find shot result determination code...")

    if Hook and Hook.markCodeScan then
        -- Look for patterns that might determine shot make/miss
        local result_patterns = {
            -- Look for random number generation calls (often used for shot success)
            "E8 ? ? ? ? 99 F7",     -- call + cdq + idiv (random number generation)
            "E8 ? ? ? ? 25 ? ? ? ?", -- call + and (masking random number)
            -- Look for shot result assignment
            "C6 ? ? 01",            -- mov byte ptr, 1 (set shot made flag)
            "C6 ? ? 00",            -- mov byte ptr, 0 (set shot missed flag)
        }

        local found_count = 0
        for i, pattern in ipairs(result_patterns) do
            local addr = Hook.markCodeScan(pattern)
            if addr and addr ~= 0 then
                print("[Fullcourt] Found potential shot result code at: " .. string.format("0x%X", addr))
                found_count = found_count + 1
            end
        end

        if found_count > 0 then
            print("[Fullcourt] Found " .. found_count .. " potential shot result determination points")
            return true
        end
    end

    return false
end

-- Plugin lifecycle functions
function Init()
    print("[Fullcourt] Auto Fullcourt Shot Plugin initializing...")

    if not fullcourt.initialized then
        -- Try multiple approaches in sequence
        print("[Fullcourt] Trying multiple shot modification approaches...")

        local success1 = fullcourt.apply_patches()
        local success2 = fullcourt.try_percentage_patches()
        local success3 = fullcourt.try_result_patches()

        if success1 or success2 or success3 then
            print("[Fullcourt] At least one modification approach succeeded")
        else
            print("[Fullcourt] All modification approaches failed - may need different patterns")
            if Hook.showNotify then
                Hook.showNotify("Fullcourt plugin loaded but no shot code found. May need updates.", 2)
            end
        end

        fullcourt.initialized = true
    end

    print("[Fullcourt] Plugin initialized successfully!")
    print("[Fullcourt] Fullcourt shot modifications attempted!")
    print("[Fullcourt] Try shooting from fullcourt distance to test effectiveness!")
    return true
end

function DeInit()
    print("[Fullcourt] Plugin deinitializing...")
    fullcourt.remove_patches()
    fullcourt.initialized = false
    print("[Fullcourt] Plugin deinitialized")
    return true
end

-- Optional: Add GUI menu item for toggling (called by the hook system)
function OnDrawMenuItem()
    if fullcourt.initialized then
        -- Simple toggle interface
        if Hook and Hook.showNotify then
            -- We could add ImGui controls here if needed
            -- For now, just ensure the plugin stays active
        end
    end
end

print("[Fullcourt] Auto Plugin loaded - " .. (PluginInfo.name or "Fullcourt Shot Auto") .. " " .. (PluginInfo.version or "V2.0"))
