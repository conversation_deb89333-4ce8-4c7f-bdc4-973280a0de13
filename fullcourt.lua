-- fullcourt.lua - NBA 2K23 Fullcourt Shot Plugin
-- Based on RosterModding.lua structure

local lang = {}
lang.EN = {
    "Fullcourt Shot",
    "Allows shooting from fullcourt distance with perfect accuracy. Press F1 to trigger."
}
lang.ZH = {
    "全场投篮",
    "允许从全场距离投篮并获得完美精度。按F1键触发。"
}

function lang.get(key)
    return lang[Hook.getGuiLang()][key]
end

-- Plugin Information (required for plugin registration)
PluginInfo = {
    name = lang.get(1),
    author = "User",
    version = "V1.0",
    description = lang.get(2),
    requiredHookVersion = {0, 0, 26}
}

-- Plugin state
local fullcourt = {
    enabled = true,
    key_pressed = false,
    initialized = false
}

-- Main shot function
function fullcourt.perform_shot()
    print("[Fullcourt] Attempting fullcourt shot...")

    -- Since we don't have direct player API, let's try a different approach
    -- Look for global game objects or try memory manipulation like other plugins

    -- Check if there are any global game-related objects
    local game_objects = {}
    for name, obj in pairs(_G) do
        if type(obj) == "table" and (
            string.find(string.lower(name), "player") or
            string.find(string.lower(name), "game") or
            string.find(string.lower(name), "court") or
            string.find(string.lower(name), "shot")
        ) then
            table.insert(game_objects, name .. " (" .. type(obj) .. ")")
        end
    end

    if #game_objects > 0 then
        print("[Fullcourt] Found potential game objects: " .. table.concat(game_objects, ", "))
    else
        print("[Fullcourt] No obvious game objects found in global scope")
    end

    -- Try using Hook.callNative or Hook.markCodeScan like other plugins do
    if Hook and Hook.callNative then
        print("[Fullcourt] Hook.callNative available - could potentially call game functions")
    end

    if Hook and Hook.markCodeScan then
        print("[Fullcourt] Hook.markCodeScan available - could potentially find game memory")
    end

    -- For now, just show a notification that the key was pressed
    if Hook and Hook.showNotify then
        Hook.showNotify("Fullcourt shot triggered! (F1 key working)", Hook.MessageType and Hook.MessageType.Info or 0)
        print("[Fullcourt] Notification sent via Hook.showNotify")
        return true
    else
        print("[Fullcourt] Hook.showNotify not available")
        return false
    end
end

-- Check for key input (called from various places)
function fullcourt.check_input()
    if not fullcourt.enabled then
        return
    end

    -- Use Hook.keyDown for key detection (F1 = VK_F1 = 0x70)
    local f1_pressed = false
    if Hook and Hook.keyDown then
        f1_pressed = Hook.keyDown(0x70)  -- F1 key
    end

    if f1_pressed then
        if not fullcourt.key_pressed then
            fullcourt.key_pressed = true
            print("[Fullcourt] F1 key detected using Hook.keyDown!")
            fullcourt.perform_shot()
        end
    else
        fullcourt.key_pressed = false
    end
end

-- Plugin lifecycle functions
function Init()
    print("[Fullcourt] Plugin initializing...")

    -- Debug: List available global functions
    print("[Fullcourt] Scanning for available API functions...")
    local api_functions = {}

    -- Check for common function patterns
    local function_patterns = {
        "get_user_player", "GetUserPlayer", "getUserPlayer",
        "is_key_pressed", "IsKeyPressed", "isKeyPressed",
        "on_frame", "OnFrame", "onFrame",
        "Hook", "hook", "Game", "Player", "Input"
    }

    for _, name in ipairs(function_patterns) do
        if _G[name] then
            table.insert(api_functions, name .. " (" .. type(_G[name]) .. ")")
        end
    end

    if #api_functions > 0 then
        print("[Fullcourt] Found API functions: " .. table.concat(api_functions, ", "))
    else
        print("[Fullcourt] No common API functions found in global scope")
    end

    -- Check Hook object if it exists
    if Hook then
        print("[Fullcourt] Hook object found, type: " .. type(Hook))
        local hook_methods = {}
        for k, v in pairs(Hook) do
            if type(v) == "function" then
                table.insert(hook_methods, k)
            end
        end
        if #hook_methods > 0 then
            print("[Fullcourt] Hook methods: " .. table.concat(hook_methods, ", "))
        end
    end

    -- Test immediate key detection
    print("[Fullcourt] Testing immediate key detection...")
    if is_key_pressed then
        local test_key = is_key_pressed(0x70)  -- F1
        print("[Fullcourt] F1 key state at init: " .. tostring(test_key))
    else
        print("[Fullcourt] is_key_pressed function not available")
    end

    fullcourt.initialized = true
    print("[Fullcourt] Plugin initialized successfully!")
    print("[Fullcourt] Press F1 in-game to trigger fullcourt shot")
    return true
end

function DeInit()
    print("[Fullcourt] Plugin deinitialized")
    fullcourt.initialized = false
    return true
end

-- Try to hook into various callback systems
function OnDrawMenuItem()
    if fullcourt.initialized then
        -- Debug: Check if this function is being called
        if not fullcourt.menu_debug_shown then
            print("[Fullcourt] OnDrawMenuItem() is being called!")
            fullcourt.menu_debug_shown = true
        end
        fullcourt.check_input()
    end
end

-- Alternative callback attempts
if Hook then
    if Hook.registerCallback then
        Hook.registerCallback("Tick", fullcourt.check_input)
    end
    if Hook.onFrame then
        Hook.onFrame = fullcourt.check_input
    end
end

print("[Fullcourt] Plugin loaded - " .. (PluginInfo.name or "Fullcourt Shot") .. " " .. (PluginInfo.version or "V1.0"))
