-- fullcourt.lua - NBA 2K23 Fullcourt Shot Plugin
-- Based on RosterModding.lua structure

local lang = {}
lang.EN = {
    "Fullcourt Shot",
    "Allows shooting from fullcourt distance with perfect accuracy. Press F1 to trigger."
}
lang.ZH = {
    "全场投篮",
    "允许从全场距离投篮并获得完美精度。按F1键触发。"
}

function lang.get(key)
    return lang[Hook.getGuiLang()][key]
end

-- Plugin Information (required for plugin registration)
PluginInfo = {
    name = lang.get(1),
    author = "User",
    version = "V1.0",
    description = lang.get(2),
    requiredHookVersion = {0, 0, 26}
}

-- Plugin state
local fullcourt = {
    enabled = true,
    key_pressed = false,
    initialized = false
}

-- Main shot function
function fullcourt.perform_shot()
    print("[Fullcourt] Attempting fullcourt shot...")

    -- Try different ways to get player
    local player = get_user_player and get_user_player() or nil
    if not player and GetUserPlayer then
        player = GetUserPlayer()
    end

    if player then
        print("[Fullcourt] Player object obtained")

        -- Try different position setting methods
        if player.set_pos then
            player:set_pos(0.0, 94.0, 0.0)
        elseif player.SetPos then
            player:SetPos(0.0, 94.0, 0.0)
        elseif player.setPosition then
            player:setPosition(0.0, 94.0, 0.0)
        end
        print("[Fullcourt] Player moved to fullcourt position")

        -- Try different shooting methods
        local result = false
        if player.shoot then
            result = player:shoot(true)
        elseif player.Shoot then
            result = player:Shoot(true)
        elseif player.performShot then
            result = player:performShot(true)
        end

        print("[Fullcourt] Shot executed, result: " .. tostring(result))
        return true
    else
        print("[Fullcourt] ERROR: Could not get player object")
        return false
    end
end

-- Check for key input (called from various places)
function fullcourt.check_input()
    if not fullcourt.enabled then
        return
    end

    -- Try different key checking methods
    local f1_pressed = false
    if is_key_pressed then
        f1_pressed = is_key_pressed(0x70)  -- F1 key
    elseif IsKeyPressed then
        f1_pressed = IsKeyPressed(0x70)
    elseif Hook and Hook.isKeyPressed then
        f1_pressed = Hook.isKeyPressed(0x70)
    end

    if f1_pressed then
        if not fullcourt.key_pressed then
            fullcourt.key_pressed = true
            print("[Fullcourt] F1 key detected!")
            fullcourt.perform_shot()
        end
    else
        fullcourt.key_pressed = false
    end
end

-- Plugin lifecycle functions
function Init()
    print("[Fullcourt] Plugin initializing...")
    fullcourt.initialized = true
    print("[Fullcourt] Plugin initialized successfully!")
    print("[Fullcourt] Press F1 in-game to trigger fullcourt shot")
    return true
end

function DeInit()
    print("[Fullcourt] Plugin deinitialized")
    fullcourt.initialized = false
    return true
end

-- Try to hook into various callback systems
function OnDrawMenuItem()
    if fullcourt.initialized then
        fullcourt.check_input()
    end
end

-- Alternative callback attempts
if Hook then
    if Hook.registerCallback then
        Hook.registerCallback("Tick", fullcourt.check_input)
    end
    if Hook.onFrame then
        Hook.onFrame = fullcourt.check_input
    end
end

print("[Fullcourt] Plugin loaded - " .. (PluginInfo.name or "Fullcourt Shot") .. " " .. (PluginInfo.version or "V1.0"))
