-- Enhanced Fullcourt Shot Script for NBA 2K23
-- Author: Enhanced by AI Assistant
-- Version: 2.0

local fullcourt = {
    -- Key states
    f1_pressed = false,
    f2_pressed = false,
    f3_pressed = false,

    -- Settings
    enabled = true,
    auto_green = true,
    shot_power = 100,

    -- Court positions (adjust these based on your court layout)
    positions = {
        fullcourt = {x = 0.0, y = 94.0, z = 0.0},    -- Opposite baseline
        halfcourt = {x = 0.0, y = 47.0, z = 0.0},    -- Half court line
        three_point = {x = 0.0, y = 23.75, z = 0.0}, -- Three point line
        free_throw = {x = 0.0, y = 19.0, z = 0.0}    -- Free throw line
    },

    -- Shot types
    shot_types = {
        NORMAL = 0,
        PERFECT = 1,
        EXCELLENT = 2
    }
}

-- Helper function to get player safely
function fullcourt.get_player()
    local player = get_user_player()
    if not player then
        print("[Fullcourt] Warning: Could not get user player")
        return nil
    end
    return player
end

-- Enhanced shooting function with different shot qualities
function fullcourt.perform_shot(shot_type, position_name)
    local player = fullcourt.get_player()
    if not player then
        return false
    end

    local pos = fullcourt.positions[position_name]
    if not pos then
        print("[Fullcourt] Error: Invalid position " .. tostring(position_name))
        return false
    end

    -- Move player to position
    player:set_pos(pos.x, pos.y, pos.z)
    print(string.format("[Fullcourt] Moved to %s (%.1f, %.1f, %.1f)",
          position_name, pos.x, pos.y, pos.z))

    -- Wait a brief moment for position to register
    -- Note: In a real implementation, you might want to add a delay mechanism

    -- Perform shot based on type
    local success = false
    if shot_type == fullcourt.shot_types.PERFECT then
        success = player:shoot(true)  -- Perfect release
        print("[Fullcourt] Executed PERFECT shot from " .. position_name)
    elseif shot_type == fullcourt.shot_types.EXCELLENT then
        success = player:shoot_excellent and player:shoot_excellent() or player:shoot(true)
        print("[Fullcourt] Executed EXCELLENT shot from " .. position_name)
    else
        success = player:shoot(false)  -- Normal shot
        print("[Fullcourt] Executed NORMAL shot from " .. position_name)
    end

    return success
end

-- Main frame function
function on_frame()
    if not fullcourt.enabled then
        return
    end

    -- F1 - Fullcourt shot with perfect release
    if is_key_pressed(0x70) then  -- F1 key
        if not fullcourt.f1_pressed then
            fullcourt.f1_pressed = true
            print("[Fullcourt] F1 - Fullcourt Perfect Shot!")
            fullcourt.perform_shot(fullcourt.shot_types.PERFECT, "fullcourt")
        end
    else
        fullcourt.f1_pressed = false
    end

    -- F2 - Half court shot
    if is_key_pressed(0x71) then  -- F2 key
        if not fullcourt.f2_pressed then
            fullcourt.f2_pressed = true
            print("[Fullcourt] F2 - Half Court Shot!")
            fullcourt.perform_shot(fullcourt.shot_types.PERFECT, "halfcourt")
        end
    else
        fullcourt.f2_pressed = false
    end

    -- F3 - Three point shot
    if is_key_pressed(0x72) then  -- F3 key
        if not fullcourt.f3_pressed then
            fullcourt.f3_pressed = true
            print("[Fullcourt] F3 - Three Point Shot!")
            fullcourt.perform_shot(fullcourt.shot_types.PERFECT, "three_point")
        end
    else
        fullcourt.f3_pressed = false
    end
end

-- Optional: Add configuration functions
function fullcourt.toggle_enabled()
    fullcourt.enabled = not fullcourt.enabled
    print("[Fullcourt] Mod " .. (fullcourt.enabled and "ENABLED" or "DISABLED"))
end

function fullcourt.set_position(position_name, x, y, z)
    if fullcourt.positions[position_name] then
        fullcourt.positions[position_name] = {x = x, y = y, z = z}
        print(string.format("[Fullcourt] Updated %s position to (%.1f, %.1f, %.1f)",
              position_name, x, y, z))
        return true
    end
    return false
end

-- Print initialization message
print("[Fullcourt] Enhanced Fullcourt Shot Script v2.0 loaded!")
print("[Fullcourt] Controls:")
print("[Fullcourt]   F1 - Fullcourt shot (opposite baseline)")
print("[Fullcourt]   F2 - Half court shot")
print("[Fullcourt]   F3 - Three point shot")
print("[Fullcourt] Status: " .. (fullcourt.enabled and "ENABLED" or "DISABLED"))
