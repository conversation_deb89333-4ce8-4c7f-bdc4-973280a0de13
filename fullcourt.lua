-- fullcourt.lua - NBA 2K23 Fullcourt Shot Plugin
-- Automatically enables fullcourt shots with perfect accuracy

local lang = {}
lang.EN = {
    "Fullcourt Shot Auto",
    "Automatically enables fullcourt shots with perfect accuracy. No key press needed - just shoot from anywhere!"
}
lang.ZH = {
    "自动全场投篮",
    "自动启用全场投篮并获得完美精度。无需按键 - 从任何地方投篮即可！"
}

function lang.get(key)
    return lang[Hook.getGuiLang()][key]
end

-- Plugin Information (required for plugin registration)
PluginInfo = {
    name = lang.get(1),
    author = "User",
    version = "V2.0",
    description = lang.get(2),
    requiredHookVersion = {0, 0, 26}
}

-- Memory patches based on the working Cheat Engine table
local patches = {
    -- First injection point - tracks shot state
    inject1 = {
        pattern = "8B 41 0C FF C8 83 F8 01 77",
        offset = 0,
        original = nil,
        address = nil
    },
    -- Second injection point - modifies shot result
    inject2 = {
        pattern = "89 B0 3C 02 00 00 48",
        offset = 0,
        original = nil,
        address = nil
    }
}

-- Plugin state
local fullcourt = {
    enabled = true,
    initialized = false,
    patches_applied = false
}

-- Apply memory patches based on the working Cheat Engine table
function fullcourt.apply_cheat_engine_patches()
    print("[Fullcourt] Applying Cheat Engine table-based patches...")

    if not Hook or not Hook.markCodeScan then
        print("[Fullcourt] Hook.markCodeScan not available")
        return false
    end

    -- Find the first injection point (shot state tracker)
    print("[Fullcourt] Scanning for first injection point...")
    local addr1 = Hook.markCodeScan(patches.inject1.pattern)

    if addr1 and addr1 ~= 0 then
        print("[Fullcourt] Found first injection point at: " .. string.format("0x%X", addr1))
        patches.inject1.address = addr1
    else
        print("[Fullcourt] First injection point not found")
        return false
    end

    -- Find the second injection point (shot result modifier)
    print("[Fullcourt] Scanning for second injection point...")
    local addr2 = Hook.markCodeScan(patches.inject2.pattern)

    if addr2 and addr2 ~= 0 then
        print("[Fullcourt] Found second injection point at: " .. string.format("0x%X", addr2))
        patches.inject2.address = addr2
    else
        print("[Fullcourt] Second injection point not found")
        return false
    end

    -- Apply the patches using assembly injection
    print("[Fullcourt] Attempting to apply assembly patches...")

    -- For now, let's try a simpler approach - just NOP out the shot result write
    -- and replace it with a forced success
    local ptr2 = Pointer(addr2)
    if ptr2 then
        -- Read original bytes
        patches.inject2.original = ptr2:readByteArrayString(6)  -- "89 B0 3C 02 00 00" is 6 bytes

        -- Replace with: mov dword ptr [rax+23C], 1 (force shot success)
        -- Assembly: C7 80 3C 02 00 00 01 00 00 00 (10 bytes)
        -- But we only have 6 bytes, so let's use: mov [rax+23C], 1 as 32-bit
        local success, err = pcall(function()
            -- This is tricky - we need to inject custom assembly
            -- For now, let's try to NOP it and see if that helps
            ptr2:writeByteArrayString("90 90 90 90 90 90")  -- 6 NOPs
        end)

        if success then
            print("[Fullcourt] Successfully applied shot result patch (NOPs)")
            if Hook.showNotify then
                Hook.showNotify("Cheat Engine patches applied! Fullcourt shots enabled!", 1)
            end
            fullcourt.patches_applied = true
            return true
        else
            print("[Fullcourt] Failed to apply shot result patch: " .. tostring(err))
        end
    end

    return false
end

-- Simplified approach - try to find and modify the exact same patterns
function fullcourt.apply_simple_patches()
    print("[Fullcourt] Trying simplified pattern matching...")

    if Hook and Hook.markCodeScan then
        -- Look for the exact patterns from the Cheat Engine table
        local ce_patterns = {
            {
                name = "shot_state_tracker",
                pattern = "8B 41 0C FF C8 83 F8 01 77",
                description = "Shot state tracking code"
            },
            {
                name = "shot_result_writer",
                pattern = "89 B0 3C 02 00 00 48",
                description = "Shot result writing code"
            }
        }

        local found_count = 0
        for _, pattern_info in ipairs(ce_patterns) do
            print("[Fullcourt] Looking for " .. pattern_info.name .. "...")
            local addr = Hook.markCodeScan(pattern_info.pattern)

            if addr and addr ~= 0 then
                print("[Fullcourt] Found " .. pattern_info.name .. " at: " .. string.format("0x%X", addr))
                found_count = found_count + 1
            else
                print("[Fullcourt] " .. pattern_info.name .. " not found")
            end
        end

        if found_count > 0 then
            if Hook.showNotify then
                Hook.showNotify("Found " .. found_count .. " Cheat Engine patterns! Analyzing...", 1)
            end
            return true
        end
    end

    return false
end

-- Remove patches (for clean shutdown)
function fullcourt.remove_patches()
    if fullcourt.patches_applied then
        print("[Fullcourt] Removing fullcourt shot patches...")

        -- Restore original bytes for the Cheat Engine patches
        if patches.inject2.address and patches.inject2.original then
            print("[Fullcourt] Restoring shot result injection point...")
            local ptr = Pointer(patches.inject2.address)
            if ptr then
                local success, err = pcall(function()
                    ptr:writeByteArrayString(patches.inject2.original)
                end)

                if success then
                    print("[Fullcourt] Successfully restored shot result code")
                else
                    print("[Fullcourt] Failed to restore shot result code: " .. tostring(err))
                end
            end
        end

        fullcourt.patches_applied = false
        print("[Fullcourt] Patch removal completed")
    end
end

-- Try a completely different approach: look for shot percentage/success rate values
function fullcourt.try_percentage_patches()
    print("[Fullcourt] Trying to find and modify shot percentage values...")

    if Hook and Hook.markCodeScan then
        -- Look for common floating point values used in shot calculations
        local percentage_patterns = {
            -- Look for 0.5 (50% success rate) - common baseline
            {pattern = "00 00 00 3F", name = "50_percent", new_value = "00 00 80 3F"},  -- Change 0.5 to 1.0
            -- Look for distance multipliers (values like 0.8, 0.9)
            {pattern = "CD CC CC 3E", name = "40_percent", new_value = "00 00 80 3F"},  -- Change 0.4 to 1.0
            {pattern = "66 66 66 3F", name = "90_percent", new_value = "00 00 80 3F"},  -- Change 0.9 to 1.0
        }

        local modified_count = 0
        for _, pct_info in ipairs(percentage_patterns) do
            local addr = Hook.markCodeScan(pct_info.pattern)
            if addr and addr ~= 0 then
                print("[Fullcourt] Found " .. pct_info.name .. " value at: " .. string.format("0x%X", addr))

                local ptr = Pointer(addr)
                if ptr then
                    local success, err = pcall(function()
                        ptr:writeByteArrayString(pct_info.new_value)
                    end)

                    if success then
                        print("[Fullcourt] Modified " .. pct_info.name .. " to 100%")
                        modified_count = modified_count + 1
                    else
                        print("[Fullcourt] Failed to modify " .. pct_info.name .. ": " .. tostring(err))
                    end
                end
            end
        end

        if modified_count > 0 then
            if Hook.showNotify then
                Hook.showNotify("Modified " .. modified_count .. " shot percentage values to 100%!", 1)
            end
            return true
        end
    end

    return false
end

-- Try to find and patch shot animation/result determination
function fullcourt.try_result_patches()
    print("[Fullcourt] Trying to find shot result determination code...")

    if Hook and Hook.markCodeScan then
        -- Look for patterns that might determine shot make/miss
        local result_patterns = {
            -- Look for random number generation calls (often used for shot success)
            "E8 ? ? ? ? 99 F7",     -- call + cdq + idiv (random number generation)
            "E8 ? ? ? ? 25 ? ? ? ?", -- call + and (masking random number)
            -- Look for shot result assignment
            "C6 ? ? 01",            -- mov byte ptr, 1 (set shot made flag)
            "C6 ? ? 00",            -- mov byte ptr, 0 (set shot missed flag)
        }

        local found_count = 0
        for i, pattern in ipairs(result_patterns) do
            local addr = Hook.markCodeScan(pattern)
            if addr and addr ~= 0 then
                print("[Fullcourt] Found potential shot result code at: " .. string.format("0x%X", addr))
                found_count = found_count + 1
            end
        end

        if found_count > 0 then
            print("[Fullcourt] Found " .. found_count .. " potential shot result determination points")
            return true
        end
    end

    return false
end

-- Plugin lifecycle functions
function Init()
    print("[Fullcourt] Auto Fullcourt Shot Plugin initializing...")

    if not fullcourt.initialized then
        print("[Fullcourt] Initializing with Cheat Engine table approach...")

        -- Try the Cheat Engine table approach first (most likely to work)
        local ce_success = fullcourt.apply_cheat_engine_patches()

        if not ce_success then
            print("[Fullcourt] Cheat Engine approach failed, trying pattern detection...")
            local simple_success = fullcourt.apply_simple_patches()

            if not simple_success then
                print("[Fullcourt] All approaches failed")
                if Hook.showNotify then
                    Hook.showNotify("Fullcourt plugin loaded but patterns not found. Game version may be different.", 2)
                end
            end
        end

        fullcourt.initialized = true
    end

    print("[Fullcourt] Plugin initialized successfully!")
    print("[Fullcourt] Fullcourt shot modifications attempted!")
    print("[Fullcourt] Try shooting from fullcourt distance to test effectiveness!")
    return true
end

function DeInit()
    print("[Fullcourt] Plugin deinitializing...")
    fullcourt.remove_patches()
    fullcourt.initialized = false
    print("[Fullcourt] Plugin deinitialized")
    return true
end

-- Optional: Add GUI menu item for toggling (called by the hook system)
function OnDrawMenuItem()
    if fullcourt.initialized then
        -- Simple toggle interface
        if Hook and Hook.showNotify then
            -- We could add ImGui controls here if needed
            -- For now, just ensure the plugin stays active
        end
    end
end

print("[Fullcourt] Auto Plugin loaded - " .. (PluginInfo.name or "Fullcourt Shot Auto") .. " " .. (PluginInfo.version or "V2.0"))
