-- fullcourt.lua - NBA 2K23 Fullcourt Shot Plugin
-- Automatically enables fullcourt shots with perfect accuracy

local lang = {}
lang.EN = {
    "Fullcourt Shot Auto",
    "Automatically enables fullcourt shots with perfect accuracy. No key press needed - just shoot from anywhere!"
}
lang.ZH = {
    "自动全场投篮",
    "自动启用全场投篮并获得完美精度。无需按键 - 从任何地方投篮即可！"
}

function lang.get(key)
    return lang[Hook.getGuiLang()][key]
end

-- Plugin Information (required for plugin registration)
PluginInfo = {
    name = lang.get(1),
    author = "User",
    version = "V2.0",
    description = lang.get(2),
    requiredHookVersion = {0, 0, 26}
}

-- Memory patches based on the working Cheat Engine table
local patches = {
    -- First injection point - tracks shot state
    inject1 = {
        pattern = "8B 41 0C FF C8 83 F8 01 77",
        offset = 0,
        original = nil,
        address = nil
    },
    -- Second injection point - modifies shot result
    inject2 = {
        pattern = "89 B0 3C 02 00 00 48",
        offset = 0,
        original = nil,
        address = nil
    }
}

-- Plugin state
local fullcourt = {
    enabled = true,
    initialized = false,
    patches_applied = false,
    gui_enabled = true,
    show_status = true,
    player_only = true,  -- New: Only affect user player shots, not CPU
    global_mode = false  -- New: When true, affects all shots (CPU + player)
}

-- Apply patches with player-only or global mode
function fullcourt.apply_patches_with_mode()
    if fullcourt.player_only then
        return fullcourt.apply_player_only_patches()
    else
        return fullcourt.apply_global_patches()
    end
end

-- Try to apply patches that only affect user player shots
function fullcourt.apply_player_only_patches()
    print("[Fullcourt] Applying player-only shot patches...")

    if not Hook or not Hook.markCodeScan then
        print("[Fullcourt] Hook.markCodeScan not available")
        return false
    end

    -- This is more complex - we need to find patterns that can distinguish
    -- between player and CPU shots. For now, let's try a conservative approach
    -- that looks for user input related shot patterns

    local player_patterns = {
        -- Look for patterns that might be related to user input timing
        "F3 0F 10 ? ? ? ? ? F3 0F 5C ? ? ? ? ? F3 0F 2F",  -- User timing calculations
        "E8 ? ? ? ? 85 C0 74 ? F3 0F 10",  -- Call + test + jump + float load (user input check)
    }

    for i, pattern in ipairs(player_patterns) do
        local addr = Hook.markCodeScan(pattern)
        if addr and addr ~= 0 then
            print("[Fullcourt] Found potential player shot pattern at: " .. string.format("0x%X", addr))
            -- For now, just log that we found it
            -- Implementing selective patching would require more reverse engineering
        end
    end

    -- Fallback: Apply global patches but warn user
    print("[Fullcourt] Player-only mode not fully implemented yet, using global mode")
    if Hook.showNotify then
        Hook.showNotify("Player-only mode not ready - using global mode (affects CPU too)", 2)
    end

    return fullcourt.apply_global_patches()
end

-- Apply global patches (affects all shots - player and CPU)
function fullcourt.apply_global_patches()
    print("[Fullcourt] Applying global shot success patches (affects ALL shots)...")

    if not Hook or not Hook.markCodeScan then
        print("[Fullcourt] Hook.markCodeScan not available")
        return false
    end

    -- Look for the shot result writing pattern
    local addr = Hook.markCodeScan("89 B0 3C 02 00 00 48")

    if addr and addr ~= 0 then
        print("[Fullcourt] Found shot result code at: " .. string.format("0x%X", addr))

        local ptr = Pointer(addr)
        if ptr then
            -- Store original bytes
            patches.inject2.original = ptr:readByteArrayString(6)
            patches.inject2.address = addr

            -- Try multiple different patches to force shot success
            local patch_attempts = {
                {
                    name = "Force ESI to 1",
                    bytes = "BE 01 00 00 00 90",  -- mov esi, 1 + nop
                    description = "Sets esi register to 1 before the write"
                },
                {
                    name = "Direct memory write",
                    bytes = "C7 80 3C 02 00 00",  -- mov dword ptr [rax+23C], (incomplete - only 6 bytes)
                    description = "Attempts direct memory write (truncated)"
                },
                {
                    name = "NOP + force",
                    bytes = "90 90 90 90 90 90",  -- All NOPs
                    description = "Disables the shot result write entirely"
                }
            }

            for i, attempt in ipairs(patch_attempts) do
                print("[Fullcourt] Trying patch attempt " .. i .. ": " .. attempt.name)

                local success, err = pcall(function()
                    ptr:writeByteArrayString(attempt.bytes)
                end)

                if success then
                    print("[Fullcourt] Successfully applied: " .. attempt.name)
                    local mode_text = fullcourt.global_mode and " (Global Mode - affects CPU too)" or ""
                    if Hook.showNotify then
                        Hook.showNotify("Applied " .. attempt.name .. " patch!" .. mode_text, 1)
                    end
                    fullcourt.patches_applied = true
                    return true
                else
                    print("[Fullcourt] Failed " .. attempt.name .. ": " .. tostring(err))
                    -- Restore original bytes before trying next attempt
                    pcall(function()
                        ptr:writeByteArrayString(patches.inject2.original)
                    end)
                end
            end
        end
    else
        print("[Fullcourt] Shot result pattern not found")
    end

    return false
end

-- Try to find and modify shot percentage values directly
function fullcourt.apply_percentage_override()
    print("[Fullcourt] Trying to override shot percentage calculations...")

    if not Hook or not Hook.markCodeScan then
        return false
    end

    -- Look for floating point comparison patterns that might control shot success
    local float_patterns = {
        "F3 0F 2F C1",  -- comiss xmm0, xmm1 (compare floats)
        "F3 0F 2F C2",  -- comiss xmm0, xmm2
        "F3 0F 2F C8",  -- comiss xmm1, xmm0
        "0F 2F C1",     -- comiss xmm0, xmm1 (shorter form)
        "0F 2F C2"      -- comiss xmm0, xmm2
    }

    local modified_count = 0
    for i, pattern in ipairs(float_patterns) do
        local addr = Hook.markCodeScan(pattern)
        if addr and addr ~= 0 then
            print("[Fullcourt] Found float comparison at: " .. string.format("0x%X", addr))

            local ptr = Pointer(addr)
            if ptr then
                local success, err = pcall(function()
                    -- Replace comparison with NOPs to always succeed
                    local nop_count = string.len(pattern:gsub(" ", "")) / 2
                    local nops = string.rep("90", nop_count)
                    ptr:writeByteArrayString(nops)
                end)

                if success then
                    print("[Fullcourt] NOPed float comparison " .. i)
                    modified_count = modified_count + 1
                end
            end
        end
    end

    if modified_count > 0 then
        if Hook.showNotify then
            Hook.showNotify("Modified " .. modified_count .. " shot comparisons! Try fullcourt shots!", 1)
        end
        return true
    end

    return false
end

-- Simplified approach - try to find and modify the exact same patterns
function fullcourt.apply_simple_patches()
    print("[Fullcourt] Trying simplified pattern matching...")

    if Hook and Hook.markCodeScan then
        -- Look for the exact patterns from the Cheat Engine table
        local ce_patterns = {
            {
                name = "shot_state_tracker",
                pattern = "8B 41 0C FF C8 83 F8 01 77",
                description = "Shot state tracking code"
            },
            {
                name = "shot_result_writer",
                pattern = "89 B0 3C 02 00 00 48",
                description = "Shot result writing code"
            }
        }

        local found_count = 0
        for _, pattern_info in ipairs(ce_patterns) do
            print("[Fullcourt] Looking for " .. pattern_info.name .. "...")
            local addr = Hook.markCodeScan(pattern_info.pattern)

            if addr and addr ~= 0 then
                print("[Fullcourt] Found " .. pattern_info.name .. " at: " .. string.format("0x%X", addr))
                found_count = found_count + 1
            else
                print("[Fullcourt] " .. pattern_info.name .. " not found")
            end
        end

        if found_count > 0 then
            if Hook.showNotify then
                Hook.showNotify("Found " .. found_count .. " Cheat Engine patterns! Analyzing...", 1)
            end
            return true
        end
    end

    return false
end

-- Toggle the fullcourt shot functionality
function fullcourt.toggle_enabled()
    if fullcourt.enabled then
        -- Disable: Remove patches
        fullcourt.remove_patches()
        fullcourt.enabled = false
        print("[Fullcourt] Fullcourt shots DISABLED")
        if Hook and Hook.showNotify then
            Hook.showNotify("Fullcourt shots DISABLED", 2)
        end
    else
        -- Enable: Apply patches
        fullcourt.enabled = true
        local success = fullcourt.apply_patches_with_mode()
        if not success then
            success = fullcourt.apply_percentage_override()
        end

        if success then
            local mode_text = fullcourt.global_mode and " (Global Mode)" or " (Player Only)"
            print("[Fullcourt] Fullcourt shots ENABLED" .. mode_text)
            if Hook and Hook.showNotify then
                Hook.showNotify("Fullcourt shots ENABLED" .. mode_text, 1)
            end
        else
            print("[Fullcourt] Failed to re-enable fullcourt shots")
            fullcourt.enabled = false
            if Hook and Hook.showNotify then
                Hook.showNotify("Failed to enable fullcourt shots", 3)
            end
        end
    end
end

-- Toggle between player-only and global mode
function fullcourt.toggle_mode()
    fullcourt.global_mode = not fullcourt.global_mode
    fullcourt.player_only = not fullcourt.global_mode

    local mode_text = fullcourt.global_mode and "Global Mode (affects CPU too)" or "Player Only Mode"
    print("[Fullcourt] Switched to: " .. mode_text)

    if Hook and Hook.showNotify then
        Hook.showNotify("Switched to: " .. mode_text, 1)
    end

    -- If currently enabled, re-apply patches with new mode
    if fullcourt.enabled then
        fullcourt.remove_patches()
        local success = fullcourt.apply_patches_with_mode()
        if not success then
            success = fullcourt.apply_percentage_override()
        end

        if not success then
            print("[Fullcourt] Failed to apply patches with new mode")
            if Hook and Hook.showNotify then
                Hook.showNotify("Failed to apply patches with new mode", 3)
            end
        end
    end
end

-- Remove patches (for clean shutdown)
function fullcourt.remove_patches()
    if fullcourt.patches_applied then
        print("[Fullcourt] Removing fullcourt shot patches...")

        -- Restore original bytes for the Cheat Engine patches
        if patches.inject2.address and patches.inject2.original then
            print("[Fullcourt] Restoring shot result injection point...")
            local ptr = Pointer(patches.inject2.address)
            if ptr then
                local success, err = pcall(function()
                    ptr:writeByteArrayString(patches.inject2.original)
                end)

                if success then
                    print("[Fullcourt] Successfully restored shot result code")
                else
                    print("[Fullcourt] Failed to restore shot result code: " .. tostring(err))
                end
            end
        end

        fullcourt.patches_applied = false
        print("[Fullcourt] Patch removal completed")
    end
end

-- Try a completely different approach: look for shot percentage/success rate values
function fullcourt.try_percentage_patches()
    print("[Fullcourt] Trying to find and modify shot percentage values...")

    if Hook and Hook.markCodeScan then
        -- Look for common floating point values used in shot calculations
        local percentage_patterns = {
            -- Look for 0.5 (50% success rate) - common baseline
            {pattern = "00 00 00 3F", name = "50_percent", new_value = "00 00 80 3F"},  -- Change 0.5 to 1.0
            -- Look for distance multipliers (values like 0.8, 0.9)
            {pattern = "CD CC CC 3E", name = "40_percent", new_value = "00 00 80 3F"},  -- Change 0.4 to 1.0
            {pattern = "66 66 66 3F", name = "90_percent", new_value = "00 00 80 3F"},  -- Change 0.9 to 1.0
        }

        local modified_count = 0
        for _, pct_info in ipairs(percentage_patterns) do
            local addr = Hook.markCodeScan(pct_info.pattern)
            if addr and addr ~= 0 then
                print("[Fullcourt] Found " .. pct_info.name .. " value at: " .. string.format("0x%X", addr))

                local ptr = Pointer(addr)
                if ptr then
                    local success, err = pcall(function()
                        ptr:writeByteArrayString(pct_info.new_value)
                    end)

                    if success then
                        print("[Fullcourt] Modified " .. pct_info.name .. " to 100%")
                        modified_count = modified_count + 1
                    else
                        print("[Fullcourt] Failed to modify " .. pct_info.name .. ": " .. tostring(err))
                    end
                end
            end
        end

        if modified_count > 0 then
            if Hook.showNotify then
                Hook.showNotify("Modified " .. modified_count .. " shot percentage values to 100%!", 1)
            end
            return true
        end
    end

    return false
end

-- Try to find and patch shot animation/result determination
function fullcourt.try_result_patches()
    print("[Fullcourt] Trying to find shot result determination code...")

    if Hook and Hook.markCodeScan then
        -- Look for patterns that might determine shot make/miss
        local result_patterns = {
            -- Look for random number generation calls (often used for shot success)
            "E8 ? ? ? ? 99 F7",     -- call + cdq + idiv (random number generation)
            "E8 ? ? ? ? 25 ? ? ? ?", -- call + and (masking random number)
            -- Look for shot result assignment
            "C6 ? ? 01",            -- mov byte ptr, 1 (set shot made flag)
            "C6 ? ? 00",            -- mov byte ptr, 0 (set shot missed flag)
        }

        local found_count = 0
        for i, pattern in ipairs(result_patterns) do
            local addr = Hook.markCodeScan(pattern)
            if addr and addr ~= 0 then
                print("[Fullcourt] Found potential shot result code at: " .. string.format("0x%X", addr))
                found_count = found_count + 1
            end
        end

        if found_count > 0 then
            print("[Fullcourt] Found " .. found_count .. " potential shot result determination points")
            return true
        end
    end

    return false
end

-- Plugin lifecycle functions
function Init()
    print("[Fullcourt] Auto Fullcourt Shot Plugin initializing...")

    if not fullcourt.initialized then
        print("[Fullcourt] Initializing fullcourt shot plugin...")
        print("[Fullcourt] Default mode: " .. (fullcourt.global_mode and "Global (affects CPU)" or "Player Only"))

        -- Try to apply patches with the current mode
        local success = fullcourt.apply_patches_with_mode()

        if not success then
            print("[Fullcourt] Primary approach failed, trying percentage override...")
            success = fullcourt.apply_percentage_override()

            if not success then
                print("[Fullcourt] Percentage approach failed, trying simple pattern detection...")
                success = fullcourt.apply_simple_patches()

                if not success then
                    print("[Fullcourt] All approaches failed")
                    if Hook.showNotify then
                        Hook.showNotify("Fullcourt plugin loaded but no effective patches found. Use GUI to retry.", 2)
                    end
                else
                    if Hook.showNotify then
                        Hook.showNotify("Fullcourt plugin loaded! Use GUI to toggle modes.", 1)
                    end
                end
            else
                if Hook.showNotify then
                    Hook.showNotify("Fullcourt plugin loaded! Use GUI to toggle modes.", 1)
                end
            end
        else
            local mode_text = fullcourt.global_mode and " (Global Mode)" or " (Player Only)"
            if Hook.showNotify then
                Hook.showNotify("Fullcourt plugin loaded!" .. mode_text, 1)
            end
        end

        fullcourt.initialized = true
    end

    print("[Fullcourt] Plugin initialized successfully!")
    print("[Fullcourt] Fullcourt shot modifications attempted!")
    print("[Fullcourt] Try shooting from fullcourt distance to test effectiveness!")
    return true
end

function DeInit()
    print("[Fullcourt] Plugin deinitializing...")
    fullcourt.remove_patches()
    fullcourt.initialized = false
    print("[Fullcourt] Plugin deinitialized")
    return true
end

-- GUI functionality for the hook system
function OnDrawMenuItem()
    if not fullcourt.initialized then
        return
    end

    -- Check if ImGui is available (it should be in the hook system)
    if ImGui then
        -- Create a menu item for our plugin
        if ImGui.BeginMenu("Fullcourt Shot") then

            -- Main toggle button
            local button_text = fullcourt.enabled and "Disable Fullcourt Shots" or "Enable Fullcourt Shots"
            if ImGui.MenuItem(button_text) then
                fullcourt.toggle_enabled()
            end

            -- Separator
            ImGui.Separator()

            -- Mode toggle button
            local mode_button_text = fullcourt.global_mode and "Switch to Player Only Mode" or "Switch to Global Mode (affects CPU)"
            if ImGui.MenuItem(mode_button_text) then
                fullcourt.toggle_mode()
            end

            -- Separator
            ImGui.Separator()

            -- Status display
            local status_text = fullcourt.enabled and "Status: ENABLED" or "Status: DISABLED"
            ImGui.Text(status_text)

            local mode_text = fullcourt.global_mode and "Mode: Global (CPU + Player)" or "Mode: Player Only"
            ImGui.Text(mode_text)

            if fullcourt.patches_applied then
                ImGui.Text("Patches: Applied")
            else
                ImGui.Text("Patches: Not Applied")
            end

            -- Separator
            ImGui.Separator()

            -- Manual controls
            if ImGui.MenuItem("Force Enable (Re-apply patches)") then
                fullcourt.enabled = true
                local success = fullcourt.apply_patches_with_mode()
                if not success then
                    success = fullcourt.apply_percentage_override()
                end

                if success and Hook.showNotify then
                    Hook.showNotify("Fullcourt patches re-applied!", 1)
                end
            end

            if ImGui.MenuItem("Force Disable (Remove patches)") then
                fullcourt.remove_patches()
                fullcourt.enabled = false
                if Hook.showNotify then
                    Hook.showNotify("Fullcourt patches removed!", 2)
                end
            end

            -- Separator
            ImGui.Separator()

            -- Info and warnings
            ImGui.Text("Plugin Version: V2.1")
            ImGui.Text("Based on Cheat Engine table")

            if fullcourt.global_mode then
                ImGui.TextColored({1.0, 0.5, 0.0, 1.0}, "WARNING: Global mode affects CPU shots too!")
            else
                ImGui.TextColored({0.0, 1.0, 0.0, 1.0}, "Player Only mode (CPU unaffected)")
            end

            ImGui.EndMenu()
        end
    else
        -- Fallback: Use simple text-based menu if ImGui not available
        -- This probably won't be called, but just in case
        print("[Fullcourt] GUI not available, use console commands")
    end
end

-- Enhanced hotkey support with multiple keys
function OnKeyDown(key)
    if not fullcourt.initialized then
        return
    end

    -- F9 key to toggle fullcourt shots on/off
    if key == 0x78 then  -- F9 key
        fullcourt.toggle_enabled()
    end

    -- F10 key to toggle between player-only and global mode
    if key == 0x79 then  -- F10 key
        fullcourt.toggle_mode()
    end

    -- F11 key for quick status check
    if key == 0x7A then  -- F11 key
        local status = fullcourt.enabled and "ENABLED" or "DISABLED"
        local mode = fullcourt.global_mode and "Global" or "Player Only"
        if Hook and Hook.showNotify then
            Hook.showNotify("Fullcourt: " .. status .. " (" .. mode .. " mode)", 1)
        end
        print("[Fullcourt] Status: " .. status .. ", Mode: " .. mode)
    end
end

-- Alternative simple toggle function that can be called from console
function toggle_fullcourt()
    if fullcourt and fullcourt.toggle_enabled then
        fullcourt.toggle_enabled()
    else
        print("[Fullcourt] Plugin not initialized")
    end
end

-- Alternative mode toggle function
function toggle_fullcourt_mode()
    if fullcourt and fullcourt.toggle_mode then
        fullcourt.toggle_mode()
    else
        print("[Fullcourt] Plugin not initialized")
    end
end

print("[Fullcourt] Auto Plugin loaded - " .. (PluginInfo.name or "Fullcourt Shot Auto") .. " " .. (PluginInfo.version or "V2.0"))
