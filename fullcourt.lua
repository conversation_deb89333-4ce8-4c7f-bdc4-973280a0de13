-- fullcourt.lua - Enhanced version
-- NBA 2K23 Hook Script for Fullcourt Shots

local key_pressed = false

function on_frame()
    -- Check if F1 is pressed
    if is_key_pressed(0x70) then  -- 0x70 is the virtual key code for F1
        if not key_pressed then
            key_pressed = true
            print("[Fullcourt Mod] Triggered full court shot!")

            local player = get_user_player()
            if player then
                -- Move player to the other side of the court
                player:set_pos(0.0, 94.0, 0.0)  -- adjust this to your court layout

                -- Simulate a green shot (if API supports it)
                player:shoot(true)  -- true = perfect release (simulated)
            end
        end
    else
        key_pressed = false
    end
end

print("[Fullcourt] Script loaded successfully!")
